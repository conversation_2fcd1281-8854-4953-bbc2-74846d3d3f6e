<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Gemini API Key 批量测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        textarea {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            background: white;
            transition: border-color 0.3s ease;
        }

        select:focus {
            outline: none;
            border-color: #667eea;
        }

        .model-info {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 13px;
            display: none;
        }

        .model-info.free {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4CAF50;
        }

        .model-info.experimental {
            background: #f3e5f5;
            color: #7b1fa2;
            border-left: 4px solid #9c27b0;
        }

        .model-info.paid {
            background: #fff3e0;
            color: #e65100;
            border-left: 4px solid #ff9800;
        }

        .model-quota {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .quota-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .quota-value {
            font-weight: bold;
        }

        .settings-row {
            display: flex;
            gap: 20px;
            align-items: end;
            margin-bottom: 20px;
        }

        .settings-item {
            flex: 1;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-section {
            margin: 30px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e1e5e9;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            color: #666;
            font-weight: 600;
        }

        .results-section {
            margin-top: 30px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
        }

        .stat-total { border-left-color: #667eea; }
        .stat-valid { border-left-color: #4CAF50; }
        .stat-invalid { border-left-color: #f44336; }
        .stat-error { border-left-color: #ff9800; }
        .stat-quota { border-left-color: #9c27b0; }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .results-grid {
            display: grid;
            gap: 15px;
        }

        .result-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease;
        }

        .result-item:hover {
            transform: translateX(5px);
        }

        .result-valid { border-left-color: #4CAF50; }
        .result-invalid { border-left-color: #f44336; }
        .result-error { border-left-color: #ff9800; }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .result-key {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #333;
        }

        .result-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-valid {
            background: #e8f5e8;
            color: #4CAF50;
        }

        .status-invalid {
            background: #ffebee;
            color: #f44336;
        }

        .status-error {
            background: #fff3e0;
            color: #ff9800;
        }

        .result-details {
            color: #666;
            font-size: 14px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            display: none;
        }

        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
            padding: 10px 15px;
            min-width: auto;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .refresh-status {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 13px;
        }

        .refresh-status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4CAF50;
        }

        .refresh-status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .refresh-status.loading {
            background: #e3f2fd;
            color: #1565c0;
            border-left: 4px solid #2196f3;
        }



        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 5px solid #f44336;
        }

        .model-select-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .model-select-container select {
            flex: 1;
        }

        .refresh-btn {
            padding: 12px 15px;
            min-width: auto;
            font-size: 16px;
            border-radius: 8px;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
        }

        .api-key-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .api-key-input {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-top: 10px;
        }

        .api-key-input input {
            flex: 1;
            font-family: 'Courier New', monospace;
        }

        .api-key-input button {
            padding: 12px 20px;
            min-width: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Google Gemini API Key 批量测试工具</h1>
        
        <div class="input-section">
            <div class="api-key-section">
                <label>🔑 API Key 配置</label>
                <div class="api-key-input">
                    <input type="password" id="singleApiKey" placeholder="输入一个API Key用于获取模型列表 (可选)" />
                    <button class="btn btn-secondary" onclick="loadModelsFromAPI()">获取模型</button>
                </div>
                <small style="color: #666; margin-top: 5px; display: block;">
                    提供API Key可获取最新模型列表，留空则使用默认模型列表
                </small>
            </div>

            <div class="input-group">
                <label for="apiKeys">API Keys (每行一个):</label>
                <textarea id="apiKeys" placeholder="AIzaSyxxxxxxxxxxxxxxxxxxxxxxx
AIzaSyxxxxxxxxxxxxxxxxxxxxxxx
AIzaSyxxxxxxxxxxxxxxxxxxxxxxx"></textarea>
            </div>
            
            <div class="input-group">
                <label for="modelSelect">选择测试模型:</label>
                <div class="model-select-container">
                    <select id="modelSelect" onchange="updateModelInfo()">
                        <option value="">加载中...</option>
                    </select>
                    <button class="btn btn-secondary refresh-btn" onclick="refreshModels()" title="刷新模型列表">
                        <span id="refreshIcon">🔄</span>
                    </button>
                </div>
                <div id="modelInfo" class="model-info"></div>
                <div id="modelLoadStatus" class="refresh-status" style="display: none;"></div>
            </div>
            
            <div class="settings-row">
                <div class="settings-item">
                    <label for="testMessage">测试消息:</label>
                    <input type="text" id="testMessage" value="Hello" placeholder="用于测试的简单消息">
                </div>
                
                <div class="settings-item">
                    <label for="concurrency">并发数:</label>
                    <input type="text" id="concurrency" value="3" placeholder="同时测试的数量">
                </div>
                
                <div class="settings-item">
                    <label for="timeout">超时时间(秒):</label>
                    <input type="text" id="timeout" value="15" placeholder="请求超时时间">
                </div>
            </div>
            
            <button class="btn btn-primary" onclick="startTesting()">
                <span id="testButtonText">开始测试</span>
            </button>
        </div>

        <div class="progress-section" id="progressSection">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">准备中...</div>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <div class="stats" id="statsSection"></div>
            <div class="results-grid" id="resultsGrid"></div>
            
            <div class="export-section" id="exportSection">
                <button class="btn btn-secondary" onclick="exportResults()">导出结果</button>
                <button class="btn btn-secondary" onclick="exportValidKeys()">导出有效Key</button>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let isRunning = false;
        let cachedModels = null;
        let modelsLoadTime = null;

        // 默认模型列表作为fallback
        const defaultModels = {
            free: [
                { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: '快速响应的多模态模型' },
                { id: 'gemini-1.5-flash-8b', name: 'Gemini 1.5 Flash 8B', description: '轻量级快速模型' },
                { id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash Latest', description: '最新版本的Flash模型' }
            ],
            paid: [
                { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: '高性能专业模型' },
                { id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro Latest', description: '最新版本的Pro模型' },
                { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', description: '新一代Flash模型' }
            ],
            experimental: [
                { id: 'gemini-2.0-flash-lite-preview', name: 'Gemini 2.0 Flash Lite Preview', description: '轻量级预览版' },
                { id: 'gemini-exp-1206', name: 'Gemini Experimental 1206', description: '实验性模型' },
                { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash Experimental', description: '实验性Flash模型' },
                { id: 'gemini-2.0-pro-exp', name: 'Gemini 2.0 Pro Experimental', description: '实验性Pro模型' },
                { id: 'gemini-2.0-flash-thinking-exp', name: 'Gemini 2.0 Flash Thinking Experimental', description: '思维链实验模型' },
                { id: 'gemini-2.5-pro-exp-03-25', name: 'Gemini 2.5 Pro Experimental 03-25', description: '2.5代实验模型' },
                { id: 'gemini-2.5-pro-preview-03-25', name: 'Gemini 2.5 Pro Preview 03-25', description: '2.5代预览模型' }
            ]
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDefaultModels();
            // 尝试自动加载模型（如果有缓存的API Key）
            const savedApiKey = localStorage.getItem('gemini_api_key');
            if (savedApiKey) {
                document.getElementById('singleApiKey').value = savedApiKey;
                loadModelsFromAPI();
            }
        });

        // 加载默认模型列表
        function loadDefaultModels() {
            populateModelSelect(defaultModels);
            updateModelLoadStatus('使用默认文本生成模型列表', 'success');
        }

        // 从API获取模型列表
        async function loadModelsFromAPI() {
            const apiKey = document.getElementById('singleApiKey').value.trim();

            if (!apiKey) {
                loadDefaultModels();
                return;
            }

            // 保存API Key到本地存储
            localStorage.setItem('gemini_api_key', apiKey);

            updateModelLoadStatus('正在获取文本生成模型列表...', 'loading');
            setRefreshButtonState(true);

            try {
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.models && data.models.length > 0) {
                    const categorizedModels = categorizeModels(data.models);
                    cachedModels = categorizedModels;
                    modelsLoadTime = new Date();

                    // 计算文本生成模型数量
                    const textModelCount = (categorizedModels.free?.length || 0) +
                                         (categorizedModels.paid?.length || 0) +
                                         (categorizedModels.experimental?.length || 0);

                    populateModelSelect(categorizedModels);
                    updateModelLoadStatus(`成功获取 ${textModelCount} 个文本生成模型`, 'success');
                } else {
                    throw new Error('未获取到模型数据');
                }

            } catch (error) {
                console.error('获取模型列表失败:', error);
                loadDefaultModels();
                updateModelLoadStatus(`获取失败: ${error.message}，使用默认列表`, 'error');
            } finally {
                setRefreshButtonState(false);
            }
        }

        // 模型分类逻辑 - 只处理文本生成模型
        function categorizeModels(models) {
            const categorized = {
                free: [],
                paid: [],
                experimental: []
            };

            models.forEach(model => {
                // 只处理支持generateContent的模型
                if (!model.supportedGenerationMethods ||
                    !model.supportedGenerationMethods.includes('generateContent')) {
                    return;
                }

                const modelId = model.baseModelId || model.name.replace('models/', '');
                const displayName = model.displayName || modelId;
                const description = model.description || '';

                // 过滤掉图像生成和其他非文本模型
                if (modelId.includes('imagen') ||
                    modelId.includes('veo') ||
                    modelId.includes('image') ||
                    modelId.includes('video') ||
                    modelId.includes('embedding') ||
                    modelId.includes('aqa')) {
                    return;
                }

                const modelInfo = {
                    id: modelId,
                    name: displayName,
                    description: description,
                    inputTokenLimit: model.inputTokenLimit,
                    outputTokenLimit: model.outputTokenLimit
                };

                // 分类逻辑
                if (modelId.includes('exp') || modelId.includes('preview') ||
                    modelId.includes('experimental') || modelId.includes('thinking')) {
                    categorized.experimental.push(modelInfo);
                } else if (modelId.includes('pro')) {
                    categorized.paid.push(modelInfo);
                } else if (modelId.includes('flash')) {
                    categorized.free.push(modelInfo);
                } else {
                    // 默认归类到付费模型
                    categorized.paid.push(modelInfo);
                }
            });

            // 按名称排序
            Object.keys(categorized).forEach(category => {
                categorized[category].sort((a, b) => a.name.localeCompare(b.name));
            });

            return categorized;
        }

        // 填充模型选择框
        function populateModelSelect(models) {
            const select = document.getElementById('modelSelect');
            select.innerHTML = '';

            // 添加免费模型
            if (models.free && models.free.length > 0) {
                const freeGroup = document.createElement('optgroup');
                freeGroup.label = '免费模型 (Free Tier)';
                models.free.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    option.dataset.description = model.description;
                    option.dataset.category = 'free';
                    freeGroup.appendChild(option);
                });
                select.appendChild(freeGroup);
            }

            // 添加付费模型
            if (models.paid && models.paid.length > 0) {
                const paidGroup = document.createElement('optgroup');
                paidGroup.label = '付费模型 (Paid)';
                models.paid.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    option.dataset.description = model.description;
                    option.dataset.category = 'paid';
                    paidGroup.appendChild(option);
                });
                select.appendChild(paidGroup);
            }

            // 添加实验性模型
            if (models.experimental && models.experimental.length > 0) {
                const expGroup = document.createElement('optgroup');
                expGroup.label = '实验性模型 (Experimental)';
                models.experimental.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    option.dataset.description = model.description;
                    option.dataset.category = 'experimental';
                    expGroup.appendChild(option);
                });
                select.appendChild(expGroup);
            }

            // 选择第一个可用模型
            if (select.options.length > 0) {
                select.selectedIndex = 0;
                updateModelInfo();
            }
        }

        // 刷新模型列表
        async function refreshModels() {
            await loadModelsFromAPI();
        }

        // 更新模型加载状态
        function updateModelLoadStatus(message, type) {
            const statusDiv = document.getElementById('modelLoadStatus');
            statusDiv.textContent = message;
            statusDiv.className = `refresh-status ${type}`;
            statusDiv.style.display = 'block';

            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 设置刷新按钮状态
        function setRefreshButtonState(loading) {
            const refreshIcon = document.getElementById('refreshIcon');
            const refreshBtn = document.querySelector('.refresh-btn');

            if (loading) {
                refreshIcon.textContent = '⏳';
                refreshBtn.disabled = true;
            } else {
                refreshIcon.textContent = '🔄';
                refreshBtn.disabled = false;
            }
        }

        // 更新模型信息显示
        function updateModelInfo() {
            const select = document.getElementById('modelSelect');
            const modelInfo = document.getElementById('modelInfo');

            if (!select.value) {
                modelInfo.style.display = 'none';
                return;
            }

            const selectedOption = select.options[select.selectedIndex];
            const category = selectedOption.dataset.category;
            const description = selectedOption.dataset.description;

            let infoHtml = '';
            let infoClass = '';

            switch (category) {
                case 'free':
                    infoClass = 'free';
                    infoHtml = `<strong>免费模型</strong><br>${description || '支持免费使用的模型'}`;
                    break;
                case 'paid':
                    infoClass = 'paid';
                    infoHtml = `<strong>付费模型</strong><br>${description || '需要付费使用的高性能模型'}`;
                    break;
                case 'experimental':
                    infoClass = 'experimental';
                    infoHtml = `<strong>实验性模型</strong><br>${description || '实验性功能，可能不稳定'}`;
                    break;
                default:
                    infoHtml = description || '';
            }

            if (infoHtml) {
                modelInfo.innerHTML = infoHtml;
                modelInfo.className = `model-info ${infoClass}`;
                modelInfo.style.display = 'block';
            } else {
                modelInfo.style.display = 'none';
            }
        }

        async function startTesting() {
            if (isRunning) return;
            
            const apiKeysText = document.getElementById('apiKeys').value.trim();
            const testMessage = document.getElementById('testMessage').value.trim() || 'Hello';
            const concurrency = parseInt(document.getElementById('concurrency').value) || 5;
            const timeout = parseInt(document.getElementById('timeout').value) || 10;
            
            if (!apiKeysText) {
                alert('请输入API Keys');
                return;
            }
            
            const apiKeys = apiKeysText.split('\n').map(key => key.trim()).filter(key => key);
            
            if (apiKeys.length === 0) {
                alert('请输入有效的API Keys');
                return;
            }
            
            isRunning = true;
            testResults = [];
            
            // 更新UI
            document.getElementById('testButtonText').innerHTML = '<span class="loading"></span> 测试中...';
            document.querySelector('.btn-primary').disabled = true;
            document.getElementById('progressSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('exportSection').style.display = 'none';
            
            // 开始测试
            await runTests(apiKeys, testMessage, concurrency, timeout * 1000);
            
            // 测试完成，更新UI
            isRunning = false;
            document.getElementById('testButtonText').textContent = '开始测试';
            document.querySelector('.btn-primary').disabled = false;
            document.getElementById('progressSection').style.display = 'none';
            
            displayResults();
        }

        async function runTests(apiKeys, testMessage, concurrency, timeout) {
            const total = apiKeys.length;
            let completed = 0;
            
            // 创建测试队列
            const testQueue = apiKeys.map((key, index) => ({ key, index }));
            const workers = [];
            
            // 创建并发工作器
            for (let i = 0; i < Math.min(concurrency, total); i++) {
                workers.push(processQueue());
            }
            
            async function processQueue() {
                while (testQueue.length > 0) {
                    const task = testQueue.shift();
                    if (!task) break;
                    
                    const result = await testSingleKey(task.key, testMessage, timeout);
                    testResults[task.index] = result;
                    
                    completed++;
                    updateProgress(completed, total);
                }
            }
            
            // 等待所有工作器完成
            await Promise.all(workers);
        }

        async function testSingleKey(apiKey, testMessage, timeout) {
            const startTime = Date.now();

            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                // 获取选中的模型
                const selectedModel = document.getElementById('modelSelect').value || 'gemini-1.5-flash';

                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${selectedModel}:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: testMessage
                            }]
                        }]
                    }),
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    return {
                        key: apiKey,
                        status: 'valid',
                        message: '测试成功',
                        responseTime: responseTime,
                        details: `响应时间: ${responseTime}ms`
                    };
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    return {
                        key: apiKey,
                        status: 'invalid',
                        message: `HTTP ${response.status}: ${response.statusText}`,
                        responseTime: responseTime,
                        details: errorData.error?.message || '未知错误'
                    };
                }
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                if (error.name === 'AbortError') {
                    return {
                        key: apiKey,
                        status: 'error',
                        message: '请求超时',
                        responseTime: responseTime,
                        details: `超时时间: ${timeout}ms`
                    };
                } else {
                    return {
                        key: apiKey,
                        status: 'error',
                        message: '网络错误',
                        responseTime: responseTime,
                        details: error.message
                    };
                }
            }
        }

        function updateProgress(completed, total) {
            const percentage = (completed / total) * 100;
            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = `已完成 ${completed}/${total} (${Math.round(percentage)}%)`;
        }

        function displayResults() {
            const validCount = testResults.filter(r => r.status === 'valid').length;
            const invalidCount = testResults.filter(r => r.status === 'invalid').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            // 显示统计信息
            const statsHtml = `
                <div class="stat-card stat-total">
                    <div class="stat-number">${testResults.length}</div>
                    <div class="stat-label">总计</div>
                </div>
                <div class="stat-card stat-valid">
                    <div class="stat-number">${validCount}</div>
                    <div class="stat-label">有效</div>
                </div>
                <div class="stat-card stat-invalid">
                    <div class="stat-number">${invalidCount}</div>
                    <div class="stat-label">无效</div>
                </div>
                <div class="stat-card stat-quota">
                    <div class="stat-number" id="quotaUsed">-</div>
                    <div class="stat-label">配额使用</div>
                </div>
            `;
            
            document.getElementById('statsSection').innerHTML = statsHtml;
            
            // 显示详细结果
            const resultsHtml = testResults.map(result => {
                const statusClass = `result-${result.status}`;
                const statusText = {
                    'valid': '有效',
                    'invalid': '无效',
                    'error': '错误'
                }[result.status];
                
                return `
                    <div class="result-item ${statusClass}">
                        <div class="result-header">
                            <div class="result-key">${maskApiKey(result.key)}</div>
                            <div class="result-status status-${result.status}">${statusText}</div>
                        </div>
                        <div class="result-details">
                            <div><strong>消息:</strong> ${result.message}</div>
                            <div><strong>详情:</strong> ${result.details}</div>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('resultsGrid').innerHTML = resultsHtml;
            document.getElementById('resultsSection').style.display = 'block';
            document.getElementById('exportSection').style.display = 'block';
        }

        function maskApiKey(key) {
            if (key.length <= 10) return key;
            return key.substring(0, 8) + '*'.repeat(Math.max(0, key.length - 16)) + key.substring(key.length - 8);
        }

        function exportResults() {
            const data = testResults.map(result => ({
                API_Key: result.key,
                状态: {
                    'valid': '有效',
                    'invalid': '无效',
                    'error': '错误'
                }[result.status],
                消息: result.message,
                响应时间: result.responseTime + 'ms',
                详情: result.details
            }));
            
            downloadJSON(data, 'gemini-api-test-results.json');
        }

        function exportValidKeys() {
            const validKeys = testResults
                .filter(result => result.status === 'valid')
                .map(result => result.key);
            
            if (validKeys.length === 0) {
                alert('没有有效的API Key可以导出');
                return;
            }
            
            const blob = new Blob([validKeys.join('\n')], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'valid-gemini-keys.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>